-- Temporary fix: Update RLS policy to allow admins to create profiles
-- Run this in your Supabase SQL editor

-- Drop the existing restrictive policy
DROP POLICY IF EXISTS "System can create profiles for new users" ON public.profiles;

-- Create a more permissive policy that allows admins to create profiles
CREATE POLICY "Admins can create profiles and users can create their own" 
ON public.profiles 
FOR INSERT 
WITH CHECK (
  -- Allow if it's the user creating their own profile (from trigger)
  auth.uid() = user_id
  OR
  -- Allow if the current user is an admin
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE user_id = auth.uid() 
    AND role = 'admin'
  )
  OR
  -- Allow if no user_id is provided (for admin-created profiles)
  user_id IS NULL
  OR
  -- Allow if user_id doesn't match any existing auth user (for pre-created profiles)
  NOT EXISTS (
    SELECT 1 FROM auth.users 
    WHERE id = user_id
  )
);
