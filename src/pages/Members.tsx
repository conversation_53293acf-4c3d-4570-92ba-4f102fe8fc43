import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Crown, Shield, Mail, Phone, Plus } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import AdminControls from '@/components/AdminControls';

const Members = () => {
  const { isAuthenticated, isAdmin } = useAuth();
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingMember, setEditingMember] = useState<any>(null);
  const [members, setMembers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: 'sub-admin' as 'admin' | 'sub-admin',
    phone: '',
    avatar_url: '',
    password: '',
  });

  const fetchMembers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setMembers(data || []);
    } catch (error) {
      console.error('Error fetching members:', error);
      toast({
        title: 'Error',
        description: 'Failed to load team members',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMembers();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.email) return;

    try {
      if (isEditing && editingMember) {
        // Update existing profile
        const { error } = await supabase
          .from('profiles')
          .update({
            name: formData.name,
            email: formData.email,
            role: formData.role, // role could be 'admin', 'sub-admin', 'user'
            phone: formData.phone,
            avatar_url: formData.avatar_url,
          })
          .eq('id', editingMember.id);

        if (error) throw error;
        toast({
          title: 'Success',
          description: 'Member updated successfully',
        });
      } else {
        // Create new user profile (simplified approach)
        if (!formData.name || !formData.email) {
          toast({
            title: 'Error',
            description: 'Name and email are required',
            variant: 'destructive',
          });
          return;
        }

        // Generate a unique temporary user_id
        const tempUserId = `temp_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`;

        // ✅ Insert profile directly (admin can do this with updated RLS policy)
        const { error: profileError } = await supabase.from('profiles').insert([
          {
            user_id: tempUserId, // temporary ID until user signs up
            name: formData.name,
            email: formData.email,
            role: formData.role,
            phone: formData.phone || null,
            avatar_url: formData.avatar_url || null,
          },
        ]);

        if (profileError) throw profileError;

        toast({
          title: 'User Profile Created',
          description: `Profile created for ${formData.email} with role: ${formData.role}. They can sign up with this email to claim their account.`,
        });
      }

      fetchMembers();
      resetForm();
    } catch (error) {
      console.error('Error saving member:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to save member',
        variant: 'destructive',
      });
    }
  };

  const handleEdit = (member: any) => {
    setEditingMember(member);
    setFormData({
      name: member.name,
      email: member.email,
      role: member.role,
      phone: member.phone || '',
      avatar_url: member.avatar_url || '',
      password: '', // Don't populate password for editing
    });
    setIsEditing(true);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this member?')) return;

    try {
      const { error } = await supabase.from('profiles').delete().eq('id', id);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Member deleted successfully',
      });
      fetchMembers();
    } catch (error) {
      console.error('Error deleting member:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete member',
        variant: 'destructive',
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      role: 'sub-admin',
      phone: '',
      avatar_url: '',
      password: '',
    });
    setIsDialogOpen(false);
    setIsEditing(false);
    setEditingMember(null);
  };

  const admins = members.filter((member) => member.role === 'admin');
  const subAdmins = members.filter((member) => member.role === 'sub-admin');

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading team members...</p>
        </div>
      </div>
    );
  }

  const MemberCard = ({ member }: { member: any }) => (
    <Card
      className="group transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border-0"
      style={{ boxShadow: 'var(--shadow-elegant)' }}
    >
      <CardContent className="p-6">
        <div className="text-center relative">
          <div className="relative mb-4">
            <img
              src={member.avatar_url || '/api/placeholder/200/200'}
              alt={member.name}
              className="w-20 h-20 rounded-full mx-auto object-cover ring-4 ring-primary/10 group-hover:ring-primary/30 transition-all duration-300"
            />
            <div className="absolute -top-1 -right-1">
              <Badge
                variant="secondary"
                className={`p-1 ${
                  member.role === 'admin'
                    ? 'bg-gradient-to-r from-accent to-yellow-400 text-accent-foreground'
                    : 'bg-gradient-to-r from-primary to-primary-glow text-primary-foreground'
                }`}
              >
                {member.role === 'admin' ? (
                  <Crown size={12} />
                ) : (
                  <Shield size={12} />
                )}
              </Badge>
            </div>
          </div>

          <h3 className="font-semibold text-lg mb-1 group-hover:text-primary transition-colors">
            {member.name}
          </h3>
          <p className="text-sm text-primary font-medium mb-3">
            {member.role === 'admin' ? 'Administrator' : 'Sub-Administrator'}
          </p>

          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <Mail size={14} />
              <span className="truncate">{member.email}</span>
            </div>
            {member.phone && (
              <div className="flex items-center justify-center gap-2 text-muted-foreground">
                <Phone size={14} />
                <span>{member.phone}</span>
              </div>
            )}
          </div>

          <div className="mt-4 flex items-center justify-center gap-2">
            <Badge variant="outline" className="text-xs">
              Since {new Date(member.created_at).getFullYear()}
            </Badge>
          </div>

          {isAdmin && (
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <AdminControls
                onEdit={() => handleEdit(member)}
                onDelete={() => handleDelete(member.id)}
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div className="text-center flex-1">
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary-glow bg-clip-text text-transparent">
              Our Team
            </h1>
            <p className="text-lg text-muted-foreground">
              Meet the dedicated administrators who keep our community thriving
            </p>
          </div>

          {isAdmin && (
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  onClick={() => {
                    setIsEditing(false);
                    setFormData({
                      name: '',
                      email: '',
                      role: 'sub-admin',
                      phone: '',
                      avatar_url: '',
                      password: '',
                    });
                  }}
                  className="bg-primary hover:bg-primary/90"
                >
                  <Plus size={16} />
                  <span className="ml-2">Add Member</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>
                    {isEditing ? 'Edit' : 'Add'} Team Member
                  </DialogTitle>
                  <DialogDescription>
                    {isEditing
                      ? 'Update member information.'
                      : 'Create a new admin or sub-admin profile. The user will need to sign up with this email to claim their account.'}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      placeholder="Enter full name"
                      value={formData.name}
                      onChange={(e) =>
                        setFormData({ ...formData, name: e.target.value })
                      }
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter email address"
                      value={formData.email}
                      onChange={(e) =>
                        setFormData({ ...formData, email: e.target.value })
                      }
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="role">Role</Label>
                    <select
                      id="role"
                      value={formData.role}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          role: e.target.value as 'admin' | 'sub-admin',
                        })
                      }
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                    >
                      <option value="sub-admin">Sub-Admin</option>
                      <option value="admin">Admin</option>
                    </select>
                  </div>
                  {!isEditing && (
                    <div className="space-y-2 bg-muted/50 p-3 rounded-md">
                      <p className="text-sm text-muted-foreground">
                        <strong>Note:</strong> This creates a user profile. The
                        user will need to sign up separately with this email
                        address to claim their account and set their password.
                      </p>
                    </div>
                  )}
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone (optional)</Label>
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="Enter phone number"
                      value={formData.phone}
                      onChange={(e) =>
                        setFormData({ ...formData, phone: e.target.value })
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="avatar_url">Avatar URL (optional)</Label>
                    <Input
                      id="avatar_url"
                      type="url"
                      placeholder="Enter avatar image URL"
                      value={formData.avatar_url}
                      onChange={(e) =>
                        setFormData({ ...formData, avatar_url: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex gap-2 pt-4">
                    <Button type="submit" className="flex-1">
                      {isEditing ? 'Update' : 'Add'} Member
                    </Button>
                    <Button type="button" variant="outline" onClick={resetForm}>
                      Cancel
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          )}
        </div>

        {/* Administrators Section */}
        <div className="mb-12">
          <div className="flex items-center gap-2 mb-6">
            <Crown className="text-accent" size={24} />
            <h2 className="text-2xl font-bold">Administrators</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {admins.map((admin) => (
              <MemberCard key={admin.id} member={admin} />
            ))}
          </div>
        </div>

        {/* Sub-Administrators Section */}
        <div>
          <div className="flex items-center gap-2 mb-6">
            <Shield className="text-primary" size={24} />
            <h2 className="text-2xl font-bold">Sub-Administrators</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {subAdmins.map((subAdmin) => (
              <MemberCard key={subAdmin.id} member={subAdmin} />
            ))}
          </div>
        </div>

        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-secondary to-muted">
            <Crown size={20} className="text-primary" />
            <span className="text-sm font-medium">
              Committed to serving our community
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Members;
