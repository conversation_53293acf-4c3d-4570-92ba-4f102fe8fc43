// Simple script to create an admin user for testing
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://bskaqpwdwpyplmqtypud.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJza2FxcHdkd3B5cGxtcXR5cHVkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUyNjQ4NjUsImV4cCI6MjA3MDg0MDg2NX0.6UCdbKubbUqEYOnkUKyA4yOgCXurn6p8pIuyQb62WQk";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function createAdminUser() {
  try {
    // Sign up a new user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'admin123!',
      options: {
        data: {
          name: 'Admin User'
        }
      }
    });

    if (authError) {
      console.error('Auth error:', authError);
      return;
    }

    console.log('User created:', authData.user?.email);

    // Wait a moment for the profile to be created by the trigger
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Update the profile to make them an admin
    if (authData.user) {
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ role: 'admin' })
        .eq('user_id', authData.user.id);

      if (updateError) {
        console.error('Profile update error:', updateError);
      } else {
        console.log('Admin user created successfully!');
        console.log('Email: <EMAIL>');
        console.log('Password: admin123!');
      }
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

createAdminUser();
