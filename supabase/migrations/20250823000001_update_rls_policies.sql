-- Update RLS policies to ensure only admins can create new users
-- This migration modifies the existing policies to be more restrictive

-- Ensure pgcrypto extension is available for password hashing
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Drop existing profile policies
DROP POLICY IF EXISTS "Users can create their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can delete their own profile" ON public.profiles;

-- Create new policies with admin restrictions

-- Allow profile creation only by the system (for new auth users) or by admins
CREATE POLICY "System can create profiles for new users" 
ON public.profiles 
FOR INSERT 
WITH CHECK (
  -- Allow if it's the user creating their own profile (from trigger)
  auth.uid() = user_id
  OR
  -- Allow if the current user is an admin
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE user_id = auth.uid() 
    AND role = 'admin'
  )
);

-- Allow users to update their own profile, but only admins can change roles
CREATE POLICY "Users can update their own profile" 
ON public.profiles 
FOR UPDATE 
USING (
  auth.uid() = user_id
  OR
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE user_id = auth.uid() 
    AND role = 'admin'
  )
)
WITH CHECK (
  -- Admins can update any profile including roles
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
  OR
  -- Users can update their own profile (role changes will be restricted by application logic)
  auth.uid() = user_id
);

-- Only admins can delete profiles
CREATE POLICY "Only admins can delete profiles" 
ON public.profiles 
FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE user_id = auth.uid() 
    AND role = 'admin'
  )
);

-- Create a simple function for user profile creation (for development)
-- This creates a profile that can be claimed when a user signs up with the same email
CREATE OR REPLACE FUNCTION public.create_user_profile_as_admin(
  user_email TEXT,
  user_name TEXT,
  user_role TEXT DEFAULT 'sub-admin',
  user_phone TEXT DEFAULT NULL,
  user_avatar_url TEXT DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  current_user_role TEXT;
  new_user_id UUID;
BEGIN
  -- Check if the current user is an admin
  SELECT role INTO current_user_role
  FROM public.profiles
  WHERE user_id = auth.uid();

  IF current_user_role != 'admin' THEN
    RAISE EXCEPTION 'Only administrators can create user profiles';
  END IF;

  -- Validate role
  IF user_role NOT IN ('admin', 'sub-admin') THEN
    RAISE EXCEPTION 'Invalid role. Must be admin or sub-admin';
  END IF;

  -- Check if user already exists
  IF EXISTS (SELECT 1 FROM public.profiles WHERE email = user_email) THEN
    RAISE EXCEPTION 'User with this email already exists';
  END IF;

  -- Generate a UUID for the user
  new_user_id := gen_random_uuid();

  -- Create the profile directly (user will need to sign up separately to claim it)
  INSERT INTO public.profiles (
    id,
    user_id,
    name,
    email,
    role,
    phone,
    avatar_url,
    created_at,
    updated_at
  ) VALUES (
    gen_random_uuid(),
    new_user_id,
    user_name,
    user_email,
    user_role,
    user_phone,
    user_avatar_url,
    now(),
    now()
  );

  RETURN new_user_id;
END;
$$;

-- Grant execute permission to authenticated users (the function checks for admin role)
GRANT EXECUTE ON FUNCTION public.create_user_profile_as_admin(TEXT, TEXT, TEXT, TEXT, TEXT) TO authenticated;
