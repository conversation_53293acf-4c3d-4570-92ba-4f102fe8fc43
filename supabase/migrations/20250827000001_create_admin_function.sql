-- Create function for admins to create user profiles
-- This function allows admins to create profiles that users can claim by signing up

CREATE OR REPLACE FUNCTION public.create_user_profile_as_admin(
  user_email TEXT,
  user_name TEXT,
  user_role TEXT DEFAULT 'sub-admin',
  user_phone TEXT DEFAULT NULL,
  user_avatar_url TEXT DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  current_user_role TEXT;
  new_user_id UUID;
BEGIN
  -- Check if the current user is an admin
  SELECT role INTO current_user_role
  FROM public.profiles
  WHERE user_id = auth.uid();

  IF current_user_role != 'admin' THEN
    RAISE EXCEPTION 'Only administrators can create user profiles';
  END IF;

  -- Validate role
  IF user_role NOT IN ('admin', 'sub-admin') THEN
    RAISE EXCEPTION 'Invalid role. Must be admin or sub-admin';
  END IF;

  -- Check if user already exists
  IF EXISTS (SELECT 1 FROM public.profiles WHERE email = user_email) THEN
    RAISE EXCEPTION 'User with this email already exists';
  END IF;

  -- Generate a UUID for the user
  new_user_id := gen_random_uuid();

  -- Create the profile directly (user will need to sign up separately to claim it)
  INSERT INTO public.profiles (
    id,
    user_id,
    name,
    email,
    role,
    phone,
    avatar_url,
    created_at,
    updated_at
  ) VALUES (
    gen_random_uuid(),
    new_user_id,
    user_name,
    user_email,
    user_role,
    user_phone,
    user_avatar_url,
    now(),
    now()
  );

  RETURN new_user_id;
END;
$$;

-- Grant execute permission to authenticated users (the function checks for admin role)
GRANT EXECUTE ON FUNCTION public.create_user_profile_as_admin(TEXT, TEXT, TEXT, TEXT, TEXT) TO authenticated;
